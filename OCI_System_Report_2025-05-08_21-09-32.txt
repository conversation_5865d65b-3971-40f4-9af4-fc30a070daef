﻿=== SYSTEM REPORT FOR ************* ===
Hostname: Cerebro
Report generated at: Thu, May 8, 2025 9:09:32 PM
n=== CPU INFORMATION ===
Architecture:        aarch64
Thread(s) per core:  1
Core(s) per socket:  4
Socket(s):           1
Model name:          Neoverse-N1
n=== MEMORY INFORMATION ===
              total        used        free      shared  buff/cache   available
Mem:           23Gi       802Mi        16Gi        13Mi       5.5Gi        22Gi
Swap:         5.0Gi          0B       5.0Gi
nMemory Details:
MemTotal:       24288932 kB
MemFree:        17733612 kB
MemAvailable:   23131764 kB
SwapTotal:       5242876 kB
SwapFree:        5242876 kB
n=== DISK INFORMATION ===
Filesystem                  Size  Used Avail Use% Mounted on
devtmpfs                     12G     0   12G   0% /dev
tmpfs                        12G     0   12G   0% /dev/shm
tmpfs                        12G  9.1M   12G   1% /run
tmpfs                        12G     0   12G   0% /sys/fs/cgroup
/dev/mapper/ocivolume-root   36G   13G   24G  34% /
/dev/sda2                   924M  329M  596M  36% /boot
/dev/mapper/ocivolume-oled   10G  174M  9.8G   2% /var/oled
/dev/sda1                   100M  7.2M   93M   8% /boot/efi
tmpfs                       2.4G     0  2.4G   0% /run/user/1000
tmpfs                       2.4G     0  2.4G   0% /run/user/986
nDisk Usage by Directory:
16K	/home
1.3G	/var
8.0K	/tmp
557M	/opt
4.9G	/usr
n=== SYSTEM LOAD ===
 04:09:32 up  1:29,  1 user,  load average: 0.06, 0.03, 0.13
nTop 5 CPU Processes:
USER         PID %CPU %MEM    VSZ   RSS TTY      STAT START   TIME COMMAND
root       53015  1.0  0.0  20780  9356 ?        Ss   04:09   0:00 sshd: opc [priv]
root           1  0.1  0.0 174636 15552 ?        Ss   02:39   0:07 /usr/lib/systemd/systemd --system --deserialize 22
root           2  0.0  0.0      0     0 ?        S    02:39   0:00 [kthreadd]
root           3  0.0  0.0      0     0 ?        I<   02:39   0:00 [rcu_gp]
root           4  0.0  0.0      0     0 ?        I<   02:39   0:00 [rcu_par_gp]
nTop 5 Memory Processes:
USER         PID %CPU %MEM    VSZ   RSS TTY      STAT START   TIME COMMAND
root       52056  0.0  0.3 1966660 81132 ?       Ssl  03:48   0:00 /usr/bin/dockerd -H fd:// --containerd=/run/containerd/containerd.sock
root        1607  0.0  0.2 298644 54424 ?        Ssl  02:40   0:01 /usr/libexec/platform-python -s /usr/sbin/firewalld --nofork --nopid
root       11853  0.0  0.1 132140 42372 ?        Ssl  03:42   0:00 /opt/unified-monitoring-agent/embedded/bin/ruby /opt/unified-monitoring-agent/embedded/bin/fluentd --log /var/log/unified-monitoring-agent/unified-monitoring-agent.log --log-rotate-size 1048576 --log-rotate-age 10
root       52041  0.0  0.1 1933708 42172 ?       Ssl  03:48   0:00 /usr/bin/containerd
root       11864  0.0  0.1 112384 35992 ?        S    03:42   0:00 /opt/unified-monitoring-agent/embedded/bin/ruby -Eascii-8bit:ascii-8bit /opt/unified-monitoring-agent/embedded/bin/fluentd --log /var/log/unified-monitoring-agent/unified-monitoring-agent.log --log-rotate-size 1048576 --log-rotate-age 10 --under-supervisor
n=== DOCKER INFORMATION ===
Docker Version:
Docker version 26.1.3, build b72abbb
nDocker Images:
REPOSITORY   TAG       IMAGE ID   CREATED   SIZE
nDocker Containers:
CONTAINER ID   IMAGE     COMMAND   CREATED   STATUS    PORTS     NAMES
nDocker Disk Usage:
TYPE            TOTAL     ACTIVE    SIZE      RECLAIMABLE
Images          0         0         0B        0B
Containers      0         0         0B        0B
Local Volumes   0         0         0B        0B
Build Cache     0         0         0B        0B
n=== NETWORK INFORMATION ===
Network Interfaces:
lo               UNKNOWN        127.0.0.1/8 ::1/128 
enp0s6           UP             **********/24 fe80::17ff:fe03:885b/64 
docker0          DOWN           **********/16 
nNetwork Connections:
Active Internet connections (only servers)
Proto Recv-Q Send-Q Local Address           Foreign Address         State      
tcp        0      0 127.0.0.1:44321         0.0.0.0:*               LISTEN     
tcp        0      0 127.0.0.1:4330          0.0.0.0:*               LISTEN     
tcp        0      0 0.0.0.0:111             0.0.0.0:*               LISTEN     
tcp        0      0 0.0.0.0:22              0.0.0.0:*               LISTEN     
tcp6       0      0 ::1:4330                :::*                    LISTEN     
tcp6       0      0 :::111                  :::*                    LISTEN     
tcp6       0      0 :::22                   :::*                    LISTEN     
tcp6       0      0 ::1:44321               :::*                    LISTEN     
udp        0      0 0.0.0.0:68              0.0.0.0:*                          
udp        0      0 0.0.0.0:111             0.0.0.0:*                          
udp        0      0 127.0.0.1:323           0.0.0.0:*                          
udp6       0      0 :::111                  :::*                               
udp6       0      0 ::1:323                 :::*                               
n=== END OF REPORT ===
