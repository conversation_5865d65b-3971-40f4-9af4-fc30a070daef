# Setup Flowise on port 3001
$sshKeyPath = "c:\Users\<USER>\Desktop\Oracle\ssh-key-2025-05-08.key"
$serverIp = "*************"
$username = "opc"

Write-Host "Setting up Flowise on port 3001..." -ForegroundColor Cyan

# Function to run a remote command
function Invoke-RemoteCommand {
    param ([string]$Command)
    Write-Host "Executing: $Command" -ForegroundColor Yellow
    & ssh -i "$sshKeyPath" "${username}@${serverIp}" $Command
}

# Step 1: Backup the original file
Write-Host "Step 1: Backing up original docker-compose.yml..." -ForegroundColor Green
Invoke-RemoteCommand "cd flowise && cp docker-compose.yml docker-compose.yml.backup"

# Step 2: Update the port configuration
Write-Host "Step 2: Updating port configuration..." -ForegroundColor Green
Invoke-RemoteCommand "cd flowise && sed -i 's/PORT=3000/PORT=3001/g' docker-compose.yml"
Invoke-RemoteCommand "cd flowise && sed -i 's/3000:3000/3001:3001/g' docker-compose.yml"
Invoke-RemoteCommand "cd flowise && sed -i 's/localhost:3000/localhost:3001/g' docker-compose.yml"

# Step 3: Verify the changes
Write-Host "Step 3: Verifying changes..." -ForegroundColor Green
Invoke-RemoteCommand "cd flowise && cat docker-compose.yml"

# Step 4: Start Flowise
Write-Host "Step 4: Starting Flowise on port 3001..." -ForegroundColor Green
Invoke-RemoteCommand "cd flowise && docker-compose up -d"

# Step 5: Check if it's running
Write-Host "Step 5: Checking if Flowise is running..." -ForegroundColor Green
Invoke-RemoteCommand "docker ps"

# Step 6: Open firewall port
Write-Host "Step 6: Opening port 3001 in firewall..." -ForegroundColor Green
Invoke-RemoteCommand "sudo firewall-cmd --permanent --add-port=3001/tcp"
Invoke-RemoteCommand "sudo firewall-cmd --reload"

Write-Host "Flowise setup complete! It should be accessible on port 3001" -ForegroundColor Green
Write-Host "URL: http://${serverIp}:3001" -ForegroundColor Yellow
Write-Host "Username: admin" -ForegroundColor Yellow
Write-Host "Password: password123" -ForegroundColor Yellow
