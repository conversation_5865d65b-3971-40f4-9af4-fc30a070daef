
---

# 🛠️ VAPI MCP Server Setup (Docker - Oracle Cloud Linux)

## 📋 Prerequisites

* SSH access to the server (already configured with the PowerShell script)
* Your **VAPI API Key** from [https://dashboard.vapi.ai](https://dashboard.vapi.ai)
* A custom port (e.g. `3000`) that you will open in your Oracle Cloud firewall
* PowerShell for running the connection script

---

## 1️⃣ Connect to Your Oracle Server

Use the provided PowerShell script to connect to your Oracle server:

```powershell
.\connect-to-oracle.ps1
```

This script will automatically use the correct SSH key and connect to your server at `*************` with username `opc`.

You can also use the script for other operations:

```powershell
# Check Docker status
.\connect-to-oracle.ps1 -DockerStatus

# List running Docker containers
.\connect-to-oracle.ps1 -DockerPs

# Run a specific Docker command
.\connect-to-oracle.ps1 -DockerCommand "images"
```

---

## 2️⃣ Create Project Directory

Once connected to the server, create a directory for the VAPI MCP server:

```bash
mkdir ~/vapi-mcp
cd ~/vapi-mcp
```

Alternatively, you can use the PowerShell script to copy files directly to the server:

```powershell
# Create a local directory with your configuration
mkdir C:\path\to\vapi-mcp
# Copy it to the server
.\connect-to-oracle.ps1 -CopyFiles -LocalPath "C:\path\to\vapi-mcp" -RemotePath "/home/<USER>/vapi-mcp"
```

---

## 3️⃣ Create the Dockerfile

```bash
nano Dockerfile
```

Paste the following contents:

```Dockerfile
FROM node:20-alpine

RUN npm install -g @vapi-ai/mcp-server

# Replace with your actual API key from https://dashboard.vapi.ai
ENV VAPI_TOKEN=<YOUR_VAPI_API_KEY>

EXPOSE 3000

ENTRYPOINT ["mcp-server"]
```

Save and exit (`Ctrl+O`, `Enter`, `Ctrl+X`)

---

## 4️⃣ Build the Docker Image

```bash
sudo docker build -t vapi-mcp-server .
```

---

## 5️⃣ Run the MCP Server in Docker

```bash
sudo docker run -d --name vapi-mcp \
  -p 3000:3000 \
  vapi-mcp-server
```

---

## 6️⃣ Verify It’s Running

```bash
sudo docker ps
sudo docker logs vapi-mcp
```

You should see logs like:

```
[MCP Server] Ready on port 3000
```

---

## 7️⃣ Open Port in Oracle Cloud

* Go to your OCI Console → **Networking → Virtual Cloud Network**
* Find your server's **Security List**
* Add an **Ingress Rule**:

  * **Source CIDR**: `0.0.0.0/0` (or restrict to your IP)
  * **Port Range**: `3000`
  * **Protocol**: `TCP`

You can verify the open ports on your server using the PowerShell script:

```powershell
.\connect-to-oracle.ps1 -CheckPorts
```

If you need to access the server from your local machine, you can set up port forwarding:

```powershell
# Forward remote port 3000 to local port 3000
.\connect-to-oracle.ps1 -PortForward 3000

# Forward remote port 3000 to a different local port (e.g., 8080)
.\connect-to-oracle.ps1 -PortForward 3000 -LocalPort 8080
```

---

## 8️⃣ Test From Your Local Machine

Test the MCP server connection using curl:

```bash
curl -H "Authorization: Bearer <YOUR_VAPI_API_KEY>" http://*************:3000/sse
```

If it connects and returns data, it’s working.

You can also monitor the server's performance and logs:

```powershell
# Check if the container is running
.\connect-to-oracle.ps1 -DockerPs

# View the container logs
.\connect-to-oracle.ps1 -DockerCommand "logs vapi-mcp"

# Generate a system report to check resource usage
.\connect-to-oracle.ps1 -SystemReport
```

---

## ✅ Next Steps

1. Go to **Vapi Dashboard → Tools**
2. Create a **new MCP Tool**
3. Set `serverUrl` to:

   ```
   http://*************:3000/sse
   ```
4. Attach this tool to your Vapi Assistant under the **Tools** tab.

Now your agent can call tools defined in your MCP server.

## 🔄 Maintenance and Troubleshooting

If you need to restart or update the MCP server:

```powershell
# Stop the container
.\connect-to-oracle.ps1 -DockerCommand "stop vapi-mcp"

# Remove the container
.\connect-to-oracle.ps1 -DockerCommand "rm vapi-mcp"

# Pull the latest image (if needed)
.\connect-to-oracle.ps1 -DockerCommand "pull node:20-alpine"

# Rebuild and run the container
# First connect to the server
.\connect-to-oracle.ps1
```

Then on the server:

```bash
cd ~/vapi-mcp
sudo docker build -t vapi-mcp-server .
sudo docker run -d --name vapi-mcp -p 3000:3000 vapi-mcp-server
```

## 📊 Monitoring

You can monitor the server's health and performance:

```powershell
# Generate a detailed system report
.\connect-to-oracle.ps1 -SystemReport -SaveReport

# Check Docker container status
.\connect-to-oracle.ps1 -DockerPs
```

---

For any questions or issues, refer to the [Vapi documentation](https://docs.vapi.ai/) or contact support.
