# connect-to-oracle.ps1
# 
# Usage examples:
#   Basic SSH connection:        .\connect-to-oracle.ps1
#   Copy files to server:        .\connect-to-oracle.ps1 -CopyFiles -LocalPath "C:\path\to\files" -RemotePath "/home/<USER>/destination"
#   Check Docker status:         .\connect-to-oracle.ps1 -DockerStatus
#   List Docker containers:      .\connect-to-oracle.ps1 -DockerPs
#   Run Docker command:          .\connect-to-oracle.ps1 -DockerCommand "images"
#   Forward container port:      .\connect-to-oracle.ps1 -PortForward 8080 -LocalPort 8000 # Maps remote 8080 to local 8000
#   Check open ports:            .\connect-to-oracle.ps1 -CheckPorts
#   Deploy webhook container:    .\connect-to-oracle.ps1 -DeployWebhook -WebhookPort 8080 -WebhookPath "/home/<USER>/webhook-app"
#   View webhook logs:           .\connect-to-oracle.ps1 -WebhookLogs
#   System resource report:      .\connect-to-oracle.ps1 -SystemReport
#   Save report to file:         .\connect-to-oracle.ps1 -SystemReport -SaveReport
param (
    [switch]$CopyFiles,
    [string]$LocalPath,
    [string]$RemotePath,
    [switch]$DockerStatus,
    [switch]$DockerPs,
    [string]$DockerCommand,
    [int]$PortForward,
    [int]$LocalPort,
    [switch]$CheckPorts,
    [switch]$DeployWebhook,
    [int]$WebhookPort = 8080,
    [string]$WebhookPath,
    [switch]$WebhookLogs,
    [switch]$SystemReport,
    [switch]$SaveReport
)

$sshKeyPath = "c:\Users\<USER>\Desktop\Oracle\ssh-key-2025-05-08.key"
$serverIp = "*************"
$username = "opc"

# Check if copying files or just connecting
# Function to run a remote command over SSH without keeping connection open
function Invoke-RemoteCommand {
    param (
        [string]$Command
    )
    
    $hostInfo = "$username@${serverIp}"
    Write-Host "Executing command on $hostInfo" -ForegroundColor Cyan
    Write-Host "Command: ${Command}" -ForegroundColor Cyan
    & ssh -i "$sshKeyPath" "${username}@${serverIp}" $Command
}

# Process the command options
if ($DockerStatus) {
    # Show Docker service status
    Invoke-RemoteCommand "sudo systemctl status docker"
    exit 0
}
elseif ($DockerPs) {
    # List running Docker containers
    Invoke-RemoteCommand "docker ps"
    exit 0
}
elseif (-not [string]::IsNullOrEmpty($DockerCommand)) {
    # Run a specific Docker command
    Invoke-RemoteCommand "docker $DockerCommand"
    exit 0
}
elseif ($CheckPorts) {
    # Check open ports on the server
    Write-Host "Checking open ports on the server..." -ForegroundColor Cyan
    Invoke-RemoteCommand "sudo ss -tulpn"
    exit 0
}
elseif ($DeployWebhook) {
    # Deploy a webhook receiver container
    if ([string]::IsNullOrEmpty($WebhookPath)) {
        Write-Host "Error: WebhookPath must be specified when using -DeployWebhook" -ForegroundColor Red
        exit 1
    }
    
    Write-Host "Deploying webhook container on port $WebhookPort..." -ForegroundColor Cyan
    
    # Create a simple webhook receiver using Node.js
    Invoke-RemoteCommand "mkdir -p $WebhookPath"
    
    # Create index.js for the webhook server
    $indexJs = 'const http = require("http");\nconst fs = require("fs");\nconst path = require("path");\n\nconst PORT = process.env.PORT || 3000;\nconst LOGS_DIR = path.join(__dirname, "logs");\n\n// Ensure logs directory exists\nif (!fs.existsSync(LOGS_DIR)) {\n    fs.mkdirSync(LOGS_DIR, { recursive: true });\n}\n\nconst server = http.createServer((req, res) => {\n    const timestamp = new Date().toISOString();\n    console.log(`[${timestamp}] Received ${req.method} request to ${req.url}`);\n    \n    let body = [];\n    req.on("data", (chunk) => {\n        body.push(chunk);\n    }).on("end", () => {\n        body = Buffer.concat(body).toString();\n        \n        // Log the webhook request\n        const logFile = path.join(LOGS_DIR, `webhook-${Date.now()}.json`);\n        const logData = {\n            timestamp,\n            method: req.method,\n            url: req.url,\n            headers: req.headers,\n            body: body ? JSON.parse(body) : {}\n        };\n        \n        fs.writeFileSync(logFile, JSON.stringify(logData, null, 2));\n        console.log(`Webhook data saved to ${logFile}`);\n        \n        // Respond to the webhook\n        res.statusCode = 200;\n        res.setHeader("Content-Type", "application/json");\n        res.end(JSON.stringify({ status: "success", message: "Webhook received" }));\n    });\n});\n\nserver.listen(PORT, () => {\n    console.log(`Webhook server running on port ${PORT}`);\n});'
    
    # Create Dockerfile for the webhook server
    $dockerfile = 'FROM node:16-alpine\nWORKDIR /app\nCOPY . .\nEXPOSE 3000\nCMD ["node", "index.js"]'
    
    # Write files to server
    Invoke-RemoteCommand "echo '$indexJs' > $WebhookPath/index.js"
    Invoke-RemoteCommand "echo '$dockerfile' > $WebhookPath/Dockerfile"
    
    # Build and run the webhook container
    Invoke-RemoteCommand "cd $WebhookPath && docker build -t webhook-receiver ."
    Invoke-RemoteCommand "docker stop webhook-receiver 2>/dev/null || true"
    Invoke-RemoteCommand "docker rm webhook-receiver 2>/dev/null || true"
    Invoke-RemoteCommand "docker run -d --name webhook-receiver -p ${WebhookPort}:3000 -v ${WebhookPath}:/app webhook-receiver"
    
    # Display the webhook URL
    Write-Host "Webhook receiver deployed successfully!" -ForegroundColor Green
    Write-Host "Webhook URL: http://${serverIp}:${WebhookPort}" -ForegroundColor Yellow
    Write-Host "Example curl command to test:" -ForegroundColor Cyan
    Write-Host "curl -X POST -H 'Content-Type: application/json' -d '{\"event\":\"test\"}' http://${serverIp}:${WebhookPort}" -ForegroundColor Yellow
    exit 0
}
elseif ($WebhookLogs) {
    # View webhook logs
    Write-Host "Retrieving webhook container logs..." -ForegroundColor Cyan
    Invoke-RemoteCommand "docker logs webhook-receiver"
    exit 0
}
elseif ($SystemReport) {
    # Generate system resource report
    Write-Host "Generating system resource report..." -ForegroundColor Cyan
    $timestamp = Get-Date -Format "yyyy-MM-dd_HH-mm-ss"
    $reportFile = "OCI_System_Report_${timestamp}.txt"
    
    # Create a multiline command to gather all system information
    $systemInfoCmd = @"
    echo "=== SYSTEM REPORT FOR ${serverIp} ===" > /tmp/system_report.txt
    echo "Hostname: $(hostname)" >> /tmp/system_report.txt
    echo "Report generated at: $(date)" >> /tmp/system_report.txt
    echo "\n=== CPU INFORMATION ===" >> /tmp/system_report.txt
    lscpu | grep -E 'Model name|Socket|Core|Thread|Architecture|CPU MHz' >> /tmp/system_report.txt
    
    echo "\n=== MEMORY INFORMATION ===" >> /tmp/system_report.txt
    free -h >> /tmp/system_report.txt
    echo "\nMemory Details:" >> /tmp/system_report.txt
    grep -i memtotal /proc/meminfo >> /tmp/system_report.txt
    grep -i memfree /proc/meminfo >> /tmp/system_report.txt
    grep -i memavailable /proc/meminfo >> /tmp/system_report.txt
    grep -i swaptotal /proc/meminfo >> /tmp/system_report.txt
    grep -i swapfree /proc/meminfo >> /tmp/system_report.txt
    
    echo "\n=== DISK INFORMATION ===" >> /tmp/system_report.txt
    df -h >> /tmp/system_report.txt
    echo "\nDisk Usage by Directory:" >> /tmp/system_report.txt
    du -sh /home /var /tmp /opt /usr 2>/dev/null >> /tmp/system_report.txt
    
    echo "\n=== SYSTEM LOAD ===" >> /tmp/system_report.txt
    uptime >> /tmp/system_report.txt
    echo "\nTop 5 CPU Processes:" >> /tmp/system_report.txt
    ps aux --sort=-%cpu | head -6 >> /tmp/system_report.txt
    echo "\nTop 5 Memory Processes:" >> /tmp/system_report.txt
    ps aux --sort=-%mem | head -6 >> /tmp/system_report.txt
    
    echo "\n=== DOCKER INFORMATION ===" >> /tmp/system_report.txt
    echo "Docker Version:" >> /tmp/system_report.txt
    docker --version 2>/dev/null >> /tmp/system_report.txt || echo "Docker not installed" >> /tmp/system_report.txt
    echo "\nDocker Images:" >> /tmp/system_report.txt
    docker images 2>/dev/null >> /tmp/system_report.txt || echo "No Docker images or Docker not running" >> /tmp/system_report.txt
    echo "\nDocker Containers:" >> /tmp/system_report.txt
    docker ps -a 2>/dev/null >> /tmp/system_report.txt || echo "No Docker containers or Docker not running" >> /tmp/system_report.txt
    echo "\nDocker Disk Usage:" >> /tmp/system_report.txt
    docker system df 2>/dev/null >> /tmp/system_report.txt || echo "Could not get Docker disk usage" >> /tmp/system_report.txt
    
    echo "\n=== NETWORK INFORMATION ===" >> /tmp/system_report.txt
    echo "Network Interfaces:" >> /tmp/system_report.txt
    ip -br addr show >> /tmp/system_report.txt
    echo "\nNetwork Connections:" >> /tmp/system_report.txt
    netstat -tuln 2>/dev/null >> /tmp/system_report.txt || ss -tuln >> /tmp/system_report.txt
    
    echo "\n=== END OF REPORT ===" >> /tmp/system_report.txt
    cat /tmp/system_report.txt
"@
    
    # Run the command to generate the report
    $report = Invoke-RemoteCommand $systemInfoCmd
    
    # Display the report
    Write-Host "$report" -ForegroundColor White
    
    # Save the report if requested
    if ($SaveReport) {
        $report | Out-File -FilePath $reportFile -Encoding utf8
        Write-Host "\nReport saved to: $reportFile" -ForegroundColor Green
    }
    
    exit 0
}
elseif ($PortForward -gt 0) {
    # Set up port forwarding for Docker container
    $localPortToUse = if ($LocalPort -gt 0) { $LocalPort } else { $PortForward }
    Write-Host "Setting up port forwarding from localhost:${localPortToUse} to remote:${PortForward}" -ForegroundColor Cyan
    & ssh -i "$sshKeyPath" -L "${localPortToUse}:localhost:${PortForward}" "${username}@${serverIp}"
    exit 0
}
elseif ($CopyFiles) {
    if ([string]::IsNullOrEmpty($LocalPath) -or [string]::IsNullOrEmpty($RemotePath)) {
        Write-Host "Error: LocalPath and RemotePath must be specified when using -CopyFiles" -ForegroundColor Red
        exit 1
    }
    
    # Create the destination by concatenating parts rather than using string interpolation
    $hostPart = "$username@$serverIp"
    Write-Host "Copying files from $LocalPath to $hostPart`:$RemotePath..." -ForegroundColor Cyan
    # Use arguments separately to avoid string interpolation issues with colons
    & scp -i "$sshKeyPath" -r "$LocalPath" "${username}@${serverIp}:$RemotePath"
} else {
    Write-Host "Connecting to $username@$serverIp..." -ForegroundColor Cyan
    # Use arguments directly to avoid string interpolation issues
    & ssh -i "$sshKeyPath" "${username}@${serverIp}"
}
