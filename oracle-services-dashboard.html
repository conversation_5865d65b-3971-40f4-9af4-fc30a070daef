<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Oracle Server Services Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .service-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: 2px solid transparent;
        }
        
        .service-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.3);
        }
        
        .service-card.n8n {
            border-color: #ff6d6d;
        }
        
        .service-card.flowise {
            border-color: #4ecdc4;
        }
        
        .service-card.demo {
            border-color: #45b7d1;
        }
        
        .service-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .service-icon {
            width: 50px;
            height: 50px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-size: 24px;
            color: white;
        }
        
        .n8n .service-icon {
            background: linear-gradient(135deg, #ff6d6d, #ff5252);
        }
        
        .flowise .service-icon {
            background: linear-gradient(135deg, #4ecdc4, #26a69a);
        }
        
        .demo .service-icon {
            background: linear-gradient(135deg, #45b7d1, #2196f3);
        }
        
        .service-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #333;
        }
        
        .service-description {
            color: #666;
            margin-bottom: 20px;
            line-height: 1.6;
        }
        
        .service-details {
            margin-bottom: 25px;
        }
        
        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            padding: 5px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .detail-label {
            font-weight: 500;
            color: #555;
        }
        
        .detail-value {
            color: #333;
            font-family: 'Courier New', monospace;
        }
        
        .access-button {
            display: inline-block;
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 10px;
            text-align: center;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        
        .access-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.3);
        }
        
        .n8n .access-button {
            background: linear-gradient(135deg, #ff6d6d, #ff5252);
        }
        
        .flowise .access-button {
            background: linear-gradient(135deg, #4ecdc4, #26a69a);
        }
        
        .demo .access-button {
            background: linear-gradient(135deg, #45b7d1, #2196f3);
        }
        
        .status {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 500;
            background: #4caf50;
            color: white;
        }
        
        .server-info {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 25px;
            color: white;
            text-align: center;
        }
        
        .server-info h3 {
            margin-bottom: 15px;
            font-size: 1.3rem;
        }
        
        .server-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .server-detail {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 10px;
        }
        
        @media (max-width: 768px) {
            .services-grid {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .service-card {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Oracle Server Dashboard</h1>
            <p>Quick access to all your self-hosted services</p>
        </div>
        
        <div class="services-grid">
            <!-- n8n Service -->
            <div class="service-card n8n">
                <div class="service-header">
                    <div class="service-icon">🔄</div>
                    <div class="service-title">n8n Automation</div>
                </div>
                <div class="service-description">
                    Workflow automation platform for connecting apps and services. Build powerful automations with a visual interface.
                </div>
                <div class="service-details">
                    <div class="detail-row">
                        <span class="detail-label">URL:</span>
                        <span class="detail-value">*************:5678</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Username:</span>
                        <span class="detail-value">admin</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Password:</span>
                        <span class="detail-value">n8n_password123</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Status:</span>
                        <span class="status">🟢 Running</span>
                    </div>
                </div>
                <a href="http://*************:5678" target="_blank" class="access-button">
                    Access n8n →
                </a>
            </div>
            
            <!-- Flowise Service -->
            <div class="service-card flowise">
                <div class="service-header">
                    <div class="service-icon">🤖</div>
                    <div class="service-title">Flowise AI</div>
                </div>
                <div class="service-description">
                    Build LLM flows and AI agents with a drag-and-drop interface. Create chatbots and AI workflows visually.
                </div>
                <div class="service-details">
                    <div class="detail-row">
                        <span class="detail-label">URL:</span>
                        <span class="detail-value">*************:3001</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Username:</span>
                        <span class="detail-value">admin</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Password:</span>
                        <span class="detail-value">password123</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Status:</span>
                        <span class="status">🟢 Running</span>
                    </div>
                </div>
                <a href="http://*************:3001" target="_blank" class="access-button">
                    Access Flowise →
                </a>
            </div>
            
            <!-- Demo Maker Service -->
            <div class="service-card demo">
                <div class="service-header">
                    <div class="service-icon">🎯</div>
                    <div class="service-title">Demo Maker</div>
                </div>
                <div class="service-description">
                    Production demo application with SSL certificate. Main application running on the server.
                </div>
                <div class="service-details">
                    <div class="detail-row">
                        <span class="detail-label">URL:</span>
                        <span class="detail-value">demo.botstack.ai</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Protocol:</span>
                        <span class="detail-value">HTTPS (SSL)</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Port:</span>
                        <span class="detail-value">3000 (proxied)</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">Status:</span>
                        <span class="status">🟢 Running</span>
                    </div>
                </div>
                <a href="https://demo.botstack.ai" target="_blank" class="access-button">
                    Access Demo →
                </a>
            </div>
        </div>
        
        <div class="server-info">
            <h3>📊 Server Information</h3>
            <p>Oracle Cloud Infrastructure - Instance: Cerebro</p>
            <div class="server-details">
                <div class="server-detail">
                    <strong>IP Address</strong><br>
                    *************
                </div>
                <div class="server-detail">
                    <strong>Architecture</strong><br>
                    ARM64 (aarch64)
                </div>
                <div class="server-detail">
                    <strong>Memory</strong><br>
                    24GB Total
                </div>
                <div class="server-detail">
                    <strong>Storage</strong><br>
                    36GB Available
                </div>
                <div class="server-detail">
                    <strong>SSH Access</strong><br>
                    opc@*************
                </div>
                <div class="server-detail">
                    <strong>Last Updated</strong><br>
                    July 31, 2025
                </div>
            </div>
        </div>
    </div>
</body>
</html>
