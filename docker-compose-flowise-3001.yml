version: '3.1'
services:
  flowise:
    image: flowiseai/flowise
    restart: always
    environment:
      - PORT=3001
      - CORS_ORIGINS=*
      - IFRAME_ORIGINS=*
      - FLOWISE_USERNAME=admin
      - FLOWISE_PASSWORD=password123
      - DATABASE_PATH=/root/.flowise
      - APIKEY_PATH=/root/.flowise
      - SECRETKEY_PATH=/root/.flowise
      - LOG_PATH=/root/.flowise/logs
      - BLOB_STORAGE_PATH=/root/.flowise/storage
    ports:
      - '3001:3001'
    volumes:
      - ~/.flowise:/root/.flowise
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:3001/api/v1/ping']
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    entrypoint: /bin/sh -c "sleep 3; flowise start"
